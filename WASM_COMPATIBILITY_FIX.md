# WebAssembly 兼容性修复说明

## 问题分析

这个浏览器插件可能影响使用了WebAssembly (WASM)的网站登录失败，主要原因如下：

### 1. 网络请求拦截 ⚠️ **主要问题**
- 插件的 `ipConnectivity.ts` 文件重写了全局的 `window.fetch` 和 `XMLHttpRequest`
- 当网络连接测试失败时，直接阻止请求发送
- WASM模块加载依赖这些网络请求，被拦截后导致加载失败

### 2. DOM大量操作
- 插件注入了大量的DOM监听器和修改器
- 可能影响页面的正常JavaScript执行环境

### 3. webRequest监听
- 背景脚本监听所有网络请求
- 可能干扰关键的认证和登录流程

## 修复方案

### 1. 网络请求代理优化 ✅ **已修复**

**修改文件**: `src/common/utils/ipConnectivity.ts`

**主要改进**:
- 添加了 `shouldSkipProxy()` 函数，识别关键请求并跳过代理
- 包括WASM文件、Service Worker、Web Worker等关键资源
- 网络测试失败时不再完全阻止请求，而是继续尝试发送
- 添加了调试日志帮助排查问题

**跳过代理的请求类型**:
```typescript
// WASM相关
'.wasm', '.wasm.gz', '_bg.wasm', 'application/wasm'

// 其他关键资源
'sw.js', 'service-worker.js', 'worker.js', '.mjs'
'.woff', '.woff2', '.ttf', '.otf'
'manifest.json'

// 认证相关
'/auth/', '/login', '/oauth', '/sso'
```

### 2. 可选的代理禁用 ✅ **已添加**

现在可以通过配置完全禁用网络代理：

```typescript
// 禁用网络代理
ipConnectivity(messageApi, { enableProxy: false })
```

## 使用建议

### 对于开发者

1. **测试环境**: 建议在测试环境中禁用网络代理
```typescript
// 在 useTranslate.tsx 中
ipConnectivity(messageApi, { enableProxy: false })
```

2. **生产环境**: 可以保持启用，但会跳过关键请求

3. **调试**: 打开浏览器控制台查看调试信息
```
跳过网络代理检查 (Fetch): https://example.com/app.wasm
跳过网络代理检查 (XHR): https://example.com/login
```

### 对于用户

如果仍然遇到WASM网站登录问题：

1. **临时解决**: 在有问题的网站上禁用插件
2. **反馈**: 向开发团队报告具体的网站和错误信息
3. **更新**: 确保使用最新版本的插件

## 验证修复

### 测试步骤

1. 安装修复后的插件版本
2. 访问之前登录失败的WASM网站
3. 检查浏览器控制台是否有相关的跳过代理日志
4. 验证登录功能是否正常

### 预期结果

- WASM文件加载不再被插件拦截
- 登录流程正常工作
- 插件的其他功能（翻译、AI助手）仍然正常

## 技术细节

### 修复前的问题代码
```typescript
// 问题：网络不通时直接return，阻止请求
if (!isConnected) {
  return  // ❌ 完全阻止请求
}
```

### 修复后的代码
```typescript
// 解决：跳过关键请求，失败时仍尝试发送
if (shouldSkipProxy(url)) {
  return originalFetch.apply(this, arguments)  // ✅ 跳过代理
}

if (!isConnected) {
  console.warn('网络连接测试失败，但仍尝试发送请求:', url)
  return originalFetch.apply(this, arguments)  // ✅ 仍然尝试
}
```

## 后续优化建议

1. **更精确的WASM检测**: 可以通过检查响应头的Content-Type来更准确识别WASM请求
2. **白名单机制**: 为特定域名或网站提供完全跳过代理的选项
3. **用户配置**: 在插件设置中提供网络代理的开关选项
4. **错误监控**: 添加更详细的错误日志和用户反馈机制

---

**修复版本**: v0.0.2  
**修复日期**: 2025-01-11  
**影响范围**: 所有使用WebAssembly的网站  
**向后兼容**: 是
